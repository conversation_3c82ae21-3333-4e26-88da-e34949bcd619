'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { GetOrderStats, GetAllOrders } from '@/api/cafeteria/menu';
import { currencyFormat } from '@/lib/utils';
import DateRangeFilter from '@/components/common/date-range-filter';
import dayjs from 'dayjs';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { ShoppingCart, Search, Eye } from 'lucide-react';
import { useSearchAndPagination } from '@/hooks/useSearchAndPagination';
import Pagination from '@/components/common/pagination';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface OrdersManagementProps {
  orderType: 'general' | 'staff' | 'special';
}

export default function OrdersManagement({ orderType }: OrdersManagementProps) {
  const { orderStats, orderStatsLoading } = GetOrderStats();
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [viewType, setViewType] = useState<'my' | 'all'>('my'); // Toggle between my orders and all orders
  const [selectedOrder, setSelectedOrder] = useState<any>(null);
  const [orderDetailsOpen, setOrderDetailsOpen] = useState(false);
  const stats = orderStats?.data;

  const {
    searchTerm,
    debouncedSearchTerm,
    handleSearchChange,
    currentPage,
    pageSize,
    handlePageChange,
    queryParam,
    setQueryParam,
  } = useSearchAndPagination({ initialPageSize: 15 });

  useEffect(() => {
    let params = [];

    if (debouncedSearchTerm) {
      params.push(`search=${debouncedSearchTerm}`);
    }

    if (startDate) {
      const formattedStartDate = dayjs(startDate).format('YYYY-MM-DD');
      params.push(`startDate=${formattedStartDate}`);
    }

    if (endDate) {
      const formattedEndDate = dayjs(endDate).format('YYYY-MM-DD');
      params.push(`endDate=${formattedEndDate}`);
    }

    // Add staff parameter based on view type
    if (viewType === 'all') {
      params.push('staff=all');
    }
    // If viewType is 'my', no staff parameter is sent (individual's orders)

    setQueryParam(params.join('&'));
  }, [debouncedSearchTerm, startDate, endDate, viewType, setQueryParam]);

  const { orders, orderLoading, mutate } = GetAllOrders(
    `?page=${currentPage}&limit=${pageSize}&${queryParam}`
  );
  const orderData = orders?.data?.orders || [];
  const totalPages = orders?.data?.totalPages ?? 0;

  console.log('Orders data:', orderData);

  const handleDateRangeChange = (
    start: Date | undefined,
    end: Date | undefined
  ) => {
    setStartDate(start);
    setEndDate(end);
  };

  const handleViewOrderDetails = (order: any) => {
    setSelectedOrder(order);
    setOrderDetailsOpen(true);
  };

  return (
    <div className="space-y-4">
      <div className="flex flex-wrap gap-3 items-center">
        <DateRangeFilter
          onDateRangeChange={handleDateRangeChange}
          className="w-[250px]"
        />
        <div className="flex gap-2">
          <Button
            variant={viewType === 'my' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewType('my')}
          >
            My Orders
          </Button>
          <Button
            variant={viewType === 'all' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewType('all')}
          >
            All Orders
          </Button>
        </div>
        <div className="relative w-64">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <Search className="w-4 h-4 text-gray-500" />
          </div>
          <Input
            type="text"
            placeholder="Search by staff ID or order details..."
            className="pl-10 pr-4 py-2 w-full"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Total Orders
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {orderStatsLoading ? (
                <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-16 rounded"></div>
              ) : (
                stats?.totalOrders || 0
              )}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {`Today's Orders`}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-amber-500">
              {orderStatsLoading ? (
                <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-16 rounded"></div>
              ) : (
                stats?.dailyOrders || 0
              )}
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              {`Today's Revenue`}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">
              {orderStatsLoading ? (
                <div className="animate-pulse bg-gray-200 dark:bg-gray-700 h-8 w-20 rounded"></div>
              ) : (
                currencyFormat(stats?.dailyRevenue || 0)
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="border rounded-md">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>S/N</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Order No</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Sales Type</TableHead>
              <TableHead>Payment</TableHead>
              <TableHead>Total Amount</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {orderLoading ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-900"></div>
                    <span className="ml-2">Loading orders...</span>
                  </div>
                </TableCell>
              </TableRow>
            ) : orderData && orderData.length > 0 ? (
              orderData.map((order: any, index: number) => (
                <TableRow key={order.id || index}>
                  <TableCell>
                    {currentPage === 1
                      ? index + 1
                      : (currentPage - 1) * pageSize + (index + 1)}
                  </TableCell>
                  <TableCell>
                    {dayjs(order.createdAt).format('YYYY-MM-DD')}
                  </TableCell>
                  <TableCell>{order.orderNumber || 'N/A'}</TableCell>
                  <TableCell>{order.orderType || 'N/A'}</TableCell>
                  <TableCell>
                    {order?.saleType?.toUpperCase() || 'N/A'}
                  </TableCell>
                  <TableCell>{order.paymentType || 'N/A'}</TableCell>
                  <TableCell>
                    {currencyFormat(order.totalAmount || 0)}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end gap-2">
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={() => handleViewOrderDetails(order)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  <div className="flex flex-col items-center justify-center text-gray-500">
                    <ShoppingCart className="h-12 w-12 mb-2 opacity-50" />
                    <p className="text-lg font-medium">No orders found</p>
                    <p className="text-sm">
                      {debouncedSearchTerm || startDate || endDate
                        ? 'Try adjusting your search or date filters'
                        : 'Orders will appear here once they are created'}
                    </p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-4">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
          />
        </div>
      )}

      {/* Order Details Modal */}
      <Dialog open={orderDetailsOpen} onOpenChange={setOrderDetailsOpen}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Order Details - {selectedOrder?.orderNumber}</DialogTitle>
          </DialogHeader>
          {selectedOrder && (
            <div className="space-y-4">
              {/* Order Information */}
              <div className="grid grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div>
                  <p className="text-sm font-medium text-gray-500">Date</p>
                  <p className="text-sm">{dayjs(selectedOrder.createdAt).format('YYYY-MM-DD HH:mm')}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Order Type</p>
                  <p className="text-sm">{selectedOrder.orderType}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Sale Type</p>
                  <p className="text-sm">{selectedOrder.saleType?.toUpperCase()}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Payment Method</p>
                  <p className="text-sm">{selectedOrder.paymentType}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Served By</p>
                  <p className="text-sm">{selectedOrder.servedBy || 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Total Amount</p>
                  <p className="text-sm font-semibold text-green-600">{currencyFormat(selectedOrder.totalAmount)}</p>
                </div>
              </div>

              {/* Order Items */}
              <div>
                <h3 className="text-lg font-semibold mb-3">Order Items</h3>
                {selectedOrder.orderItems && selectedOrder.orderItems.length > 0 ? (
                  <div className="space-y-2">
                    {selectedOrder.orderItems.map((item: any, index: number) => (
                      <div key={index} className="flex justify-between items-center p-3 border rounded-lg">
                        <div>
                          <p className="font-medium">{item.menuItem?.name || item.name || 'Unknown Item'}</p>
                          <p className="text-sm text-gray-500">Quantity: {item.quantity}</p>
                          <p className="text-sm text-gray-500">Unit Price: {currencyFormat(item.unitPrice || 0)}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold">{currencyFormat((item.quantity || 0) * (item.unitPrice || 0))}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500 text-center py-4">No items found for this order</p>
                )}
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
