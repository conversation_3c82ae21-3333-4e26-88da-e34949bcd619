if (!self.define) {
  let e,
    a = {};
  const n = (n, s) => (
    (n = new URL(n + '.js', s).href),
    a[n] ||
      new Promise((a) => {
        if ('document' in self) {
          const e = document.createElement('script');
          (e.src = n), (e.onload = a), document.head.appendChild(e);
        } else (e = n), importScripts(n), a();
      }).then(() => {
        let e = a[n];
        if (!e) throw new Error(`Module ${n} didn’t register its module`);
        return e;
      })
  );
  self.define = (s, t) => {
    const i =
      e ||
      ('document' in self ? document.currentScript.src : '') ||
      location.href;
    if (a[i]) return;
    let c = {};
    const d = (e) => n(e, i),
      r = { module: { uri: i }, exports: c, require: d };
    a[i] = Promise.all(s.map((e) => r[e] || d(e))).then((e) => (t(...e), c));
  };
}
define(['./workbox-1bb06f5e'], function (e) {
  'use strict';
  importScripts(),
    self.skipWaiting(),
    e.clientsClaim(),
    e.precacheAndRoute(
      [
        {
          url: '/_next/app-build-manifest.json',
          revision: '834d30ab8d77e39434b0aa94eb4f7443',
        },
        {
          url: '/_next/static/3aWkCbnOez2Mw_dUF6Q3y/_buildManifest.js',
          revision: 'ec734bf20f4c3b379a4660a8d2dd30af',
        },
        {
          url: '/_next/static/3aWkCbnOez2Mw_dUF6Q3y/_ssgManifest.js',
          revision: 'b6652df95db52feb4daf4eca35380933',
        },
        {
          url: '/_next/static/chunks/1044-989d46e3553a5261.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/1078-5dc9f867a616958f.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/1684-2024d14f0d42f258.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/186-8d1910733644a6fb.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/1887-04d958d6754f21f1.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/2289-17b1d726adb01766.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/2441-761faccc465dbbef.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/259-69d7deb30115c0c9.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/278-8a25713f9a9bdaa7.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/2950-469b40ec3a61b8c1.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/2982-423d748aae90a6e0.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/3464-17e00d9ba44f28f7.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/3605-d01460ee32fa5c6f.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/428-4490300c62dadb3c.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/472.2c08b965bd9148e2.js',
          revision: '2c08b965bd9148e2',
        },
        {
          url: '/_next/static/chunks/4792-59a9c920f5f088f8.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/4945-4d0ef61a214c9d71.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/4bd1b696-9d7633aa5bd4fe4f.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/5157-3549aa120e199458.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/5521-171c5b9d659d6c15.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/5600-05fe265a24e5887d.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/5766-baba0607c813113c.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/6069-ead01464727496cf.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/6240-802e2cb968ba6eb3.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/6671-e6da9dd8068cdc87.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/6730-55a07d516f8e69f3.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/6766-afa4461e8652e50a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/6874-49b8f86bf7767823.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/7031-0ad17144d34b3b15.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/7039-f113b154cf85b360.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/7096-679d45f5b5242c6b.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/8055-6b8c825b410f4611.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/8469-fc65f7730383acab.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/8617-f9054e3ce89dc253.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/8746-d2475f96837b867a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/879-a20ba80222509cab.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/9269-0021d42b34a8e584.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/9293-790b0f29255542ae.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/9341.c00e1bbc955787be.js',
          revision: 'c00e1bbc955787be',
        },
        {
          url: '/_next/static/chunks/9388-6169167ec77d674c.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/9481-ce68daa3d6d4cfdb.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/9519-918d92a362862377.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/9638-8cd796d7ab183919.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/998-f38b4aef2b28a380.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/cafeteria/page-477013cbd88fd8a6.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/dashboard/page-b1053d619d12935f.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/data-analysis/page-11e64adab91fe238.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/discounts/discount-code/page-00a6b3457754f978.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/discounts/page-c2f7b79a41c1f135.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/feedbacks/page-9215648372c98483.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/direct-messages/page-339d491ae6b95ebb.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/groups/%5BgroupId%5D/page-8fba195891c98a5b.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/forum/page-4332fd7ffa5edf5e.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/incident-reporting/page-140593da776eb55e.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/innovation-hub/page-cae660c485b2bcb0.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/layout-48f0e7d10ccdc549.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/locations/page-afb0366e44697a5a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/manage-staffs/page-89d94a995a18b4b1.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/edit-package/%5Bslug%5D/page-8e1be87499996fa3.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/investigation/page-78e73e4a7b3603a6.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/manage-category/page-b16c2e842cd6c529.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/manage-package/page-c687336b0b4d5c72.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/new-package/page-7a60262a133067d0.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/packages/page-3b1a223bcd79b50d.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/analytics/page-2477f1aea2f7ee9d.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/appointments/page-f4cc29ce32869626.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/departments/page-c0737ad43c4d87dd.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/doctors/page-ab2f98e97a3bc7a9.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/interactions/page-4ac8c7566f4c3fc8.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/medical-records/page-a86aa5e8e26607aa.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/page-f5e6dd161afa4b3d.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/patient-management/patients/page-48319b0bebc10d7a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/process-dictionary/page-4448791a770d233d.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/page-96c6e45b633010ec.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/referring-entities/page-3784de5144280670.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/referrals/rewards/page-3e6164c0ef6cd0e6.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/rewards/page-473158b706e037e0.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/system-settings/page-744f32f9972fecb9.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/(authenticated)/transactions/page-22a0fdf5a3fb52a4.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/_not-found/page-72fa00e5a996b855.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/error-6492789314180dea.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/forbidden/page-c8ecdc5976d4f32a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/layout-5571dfe0c0febb6a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/login/page-7398b7d7402ad8af.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/app/page-73c79a8d27ba3dbe.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/framework-c054b661e612b06c.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/main-app-15ab57bde25404f0.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/main-bf16eb4740561336.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/pages/_app-4e5feef8afe3c684.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/pages/_error-faae17b7f45ea4ac.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/chunks/polyfills-42372ed130431b0a.js',
          revision: '846118c33b2c0e922d7b3a7676f81f6f',
        },
        {
          url: '/_next/static/chunks/webpack-585805a6031f1a0a.js',
          revision: '3aWkCbnOez2Mw_dUF6Q3y',
        },
        {
          url: '/_next/static/css/3d226f19e2f51a22.css',
          revision: '3d226f19e2f51a22',
        },
        {
          url: '/_next/static/media/034d78ad42e9620c-s.woff2',
          revision: 'be7c930fceb794521be0a68e113a71d8',
        },
        {
          url: '/_next/static/media/29a4aea02fdee119-s.woff2',
          revision: '69d9d2cdadeab7225297d50fc8e48e8b',
        },
        {
          url: '/_next/static/media/4c285fdca692ea22-s.p.woff2',
          revision: '42d3308e3aca8742731f63154187bdd7',
        },
        {
          url: '/_next/static/media/6c177e25b87fd9cd-s.woff2',
          revision: '4f9434d4845212443bbd9d102f1f5d7d',
        },
        {
          url: '/_next/static/media/6c9a125e97d835e1-s.woff2',
          revision: '889718d692d5bfc6019cbdfcb5cc106f',
        },
        {
          url: '/_next/static/media/a1386beebedccca4-s.woff2',
          revision: 'd3aa06d13d3cf9c0558927051f3cb948',
        },
        {
          url: '/_next/static/media/b957ea75a84b6ea7-s.p.woff2',
          revision: '0bd523f6049956faaf43c254a719d06a',
        },
        {
          url: '/_next/static/media/eafabf029ad39a43-s.p.woff2',
          revision: '43751174b6b810eb169101a20d8c26f8',
        },
        {
          url: '/_next/static/media/fe0777f1195381cb-s.woff2',
          revision: 'f2a04185547c36abfa589651236a9849',
        },
        {
          url: '/_next/static/media/icon.0c4b6864.png',
          revision: '6c32b251eefc1a2fe5730ea8f1cda7fb',
        },
        {
          url: '/_next/static/media/logo.7bb171e6.png',
          revision: 'c4ff5bcbd8371f70a6b4dee2b692f68c',
        },
        { url: '/icon-512.png', revision: '6c32b251eefc1a2fe5730ea8f1cda7fb' },
        { url: '/icon.png', revision: '6c32b251eefc1a2fe5730ea8f1cda7fb' },
        { url: '/manifest.json', revision: '506f7804edbf5541ab40326a24d01d3d' },
        {
          url: '/whatsapp-bg.png',
          revision: '6264a93f7bf5f59d4512c2692a6f18e7',
        },
      ],
      { ignoreURLParametersMatching: [] }
    ),
    e.cleanupOutdatedCaches(),
    e.registerRoute(
      '/',
      new e.NetworkFirst({
        cacheName: 'start-url',
        plugins: [
          {
            cacheWillUpdate: async ({
              request: e,
              response: a,
              event: n,
              state: s,
            }) =>
              a && 'opaqueredirect' === a.type
                ? new Response(a.body, {
                    status: 200,
                    statusText: 'OK',
                    headers: a.headers,
                  })
                : a,
          },
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /^https:\/\/fonts\.(?:gstatic)\.com\/.*/i,
      new e.CacheFirst({
        cacheName: 'google-fonts-webfonts',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 31536e3 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /^https:\/\/fonts\.(?:googleapis)\.com\/.*/i,
      new e.StaleWhileRevalidate({
        cacheName: 'google-fonts-stylesheets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:eot|otf|ttc|ttf|woff|woff2|font.css)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-font-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 4, maxAgeSeconds: 604800 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:jpg|jpeg|gif|png|svg|ico|webp)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-image-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\/_next\/image\?url=.+$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'next-image',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 64, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:mp3|wav|ogg)$/i,
      new e.CacheFirst({
        cacheName: 'static-audio-assets',
        plugins: [
          new e.RangeRequestsPlugin(),
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:mp4)$/i,
      new e.CacheFirst({
        cacheName: 'static-video-assets',
        plugins: [
          new e.RangeRequestsPlugin(),
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:js)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-js-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:css|less)$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'static-style-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\/_next\/data\/.+\/.+\.json$/i,
      new e.StaleWhileRevalidate({
        cacheName: 'next-data',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      /\.(?:json|xml|csv)$/i,
      new e.NetworkFirst({
        cacheName: 'static-data-assets',
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => {
        if (!(self.origin === e.origin)) return !1;
        const a = e.pathname;
        return !a.startsWith('/api/auth/') && !!a.startsWith('/api/');
      },
      new e.NetworkFirst({
        cacheName: 'apis',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 16, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => {
        if (!(self.origin === e.origin)) return !1;
        return !e.pathname.startsWith('/api/');
      },
      new e.NetworkFirst({
        cacheName: 'others',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 86400 }),
        ],
      }),
      'GET'
    ),
    e.registerRoute(
      ({ url: e }) => !(self.origin === e.origin),
      new e.NetworkFirst({
        cacheName: 'cross-origin',
        networkTimeoutSeconds: 10,
        plugins: [
          new e.ExpirationPlugin({ maxEntries: 32, maxAgeSeconds: 3600 }),
        ],
      }),
      'GET'
    );
});
