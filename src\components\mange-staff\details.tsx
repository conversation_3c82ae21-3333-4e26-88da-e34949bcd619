import React, { useState } from 'react';
import { Modal } from '@/components/common/modal';
import { ModalProps } from '../types';
import { Button } from '@/components/ui/button';
import { myApi } from '@/api/fetcher';
import { toast } from 'sonner';
import { Loader2, User, Award, ClipboardCheck } from 'lucide-react';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { StatusBadge } from '@/components/common/status-badge';
import { FormRow } from '../common/form';
import { numberFormat } from '@/lib/utils';

// Tab interface
interface TabProps {
  label: string;
  icon: React.ReactNode;
  content: React.ReactNode;
}

const Details: React.FC<ModalProps> = ({ setOpen, open, mutate, data }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState(0);

  // Function to toggle staff active status
  const handleToggleStatus = async () => {
    try {
      setIsLoading(true);
      const res = await myApi.patch('/staff/update', {
        id: data.id,
        isActive: !data.isActive,
      });
      setIsLoading(false);
      if (res.status === 200) {
        toast.success(res.data.message || 'Staff status updated successfully');
        setOpen(false);
        if (mutate) {
          mutate();
        }
      }
    } catch (error) {
      setIsLoading(false);
    }
  };

  // Personal Information Tab Content
  const PersonalInfo = () => (
    <FormRow>
      <div>
        <p className="text-sm font-medium">Full Name</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {data.fullName}
        </p>
      </div>
      <div>
        <p className="text-sm font-medium">Email</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{data.email}</p>
      </div>
      <div>
        <p className="text-sm font-medium">Phone Number</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {data.phoneNumber || 'N/A'}
        </p>
      </div>
      <div>
        <p className="text-sm font-medium">Staff ID</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {data.staffCode || 'N/A'}
        </p>
      </div>
      <div>
        <p className="text-sm font-medium">Location</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {data.location.name}, {data.location.region}
        </p>
      </div>
      <div>
        <p className="text-sm font-medium">Staff Type</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">{data.type}</p>
      </div>
      <div>
        <p className="text-sm font-medium">Department</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {data.department.name}
        </p>
      </div>
      {data.unitId && (
        <div>
          <p className="text-sm font-medium">Unit</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data.unit.name}
          </p>
        </div>
      )}
      {data.isDoctor && (
        <>
          <div>
            <p className="text-sm font-medium">Consultant</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.isConsultant
                ? data.isVisitingConsultant
                  ? 'Visiting Consultant'
                  : 'Yes'
                : 'No'}
            </p>
          </div>
          <div>
            <p className="text-sm font-medium">Specialty</p>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {data.specialty}
            </p>
          </div>
        </>
      )}
      <div>
        <p className="text-sm font-medium">Status</p>
        <div className="mt-1">
          {data.isActive
            ? StatusBadge({ status: 'active' })
            : StatusBadge({ status: 'inactive' })}
        </div>
      </div>
      <div>
        <p className="text-sm font-medium">Date Created</p>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          {dayjs(data.createdAt).format('MMMM D, YYYY')}
        </p>
      </div>
    </FormRow>
  );

  // Referral Information Tab Content
  const ReferralInfo = () => (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div>
          <p className="text-sm font-medium">Referral Code</p>
          <div className="flex items-center gap-2 mt-1">
            <Badge variant="outline" className="px-3 py-1 text-sm">
              {data.referralCode?.code || 'N/A'}
            </Badge>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => {
                navigator.clipboard.writeText(data.referralCode?.code || '');
                toast.success('Referral code copied to clipboard');
              }}
            >
              <ClipboardCheck className="w-4 h-4" />
            </Button>
          </div>
        </div>
        <div>
          <p className="text-sm font-medium">Code Usage</p>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {data.codeUsage || '0'} times
          </p>
        </div>
        <div>
          <p className="text-sm font-medium">Total Amount</p>
          {numberFormat(data.total)}
        </div>
        <div>
          <p className="text-sm font-medium">Wallet Balance</p>
          {numberFormat(data.wallet)}
        </div>
      </div>
    </div>
  );

  const tabs: TabProps[] = [
    {
      label: 'Personal Info',
      icon: <User className="w-4 h-4" />,
      content: <PersonalInfo />,
    },
    {
      label: 'Referral & Wallet',
      icon: <ClipboardCheck className="w-4 h-4" />,
      content: <ReferralInfo />,
    },
  ];

  return (
    <Modal
      open={open}
      setOpen={setOpen}
      title="Staff Details"
      description={`Staff information for ${data?.fullName || 'Unknown'}`}
      size="lg"
    >
      {/* Tabs */}
      <div className="flex border-b mb-4">
        {tabs.map((tab, index) => (
          <button
            key={index}
            className={cn(
              'flex items-center gap-1 px-4 py-2 text-sm font-medium',
              activeTab === index
                ? 'border-b-2 border-primary text-primary'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
            )}
            onClick={() => setActiveTab(index)}
          >
            {tab.icon}
            {tab.label}
          </button>
        ))}
      </div>

      {/* Tab content */}
      <div className="py-2 mb-4">{tabs[activeTab].content}</div>

      {/* Action buttons */}
      <div className="flex justify-end gap-2 mt-4">
        <Button variant="outline" onClick={() => setOpen(false)}>
          Close
        </Button>
        <Button
          variant={data?.isActive ? 'destructive' : 'default'}
          onClick={handleToggleStatus}
          disabled={isLoading}
        >
          {isLoading ? (
            <Loader2 className="w-4 h-4 mr-2 animate-spin" />
          ) : null}
          {data?.isActive ? 'Deactivate Staff' : 'Activate Staff'}
        </Button>
      </div>
    </Modal>
  );
};

export default Details;
